actor: "<PERSON>EA<PERSON> TABLE `actor` (\n  `actor_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `first_name` varchar(45) NOT NULL,\n  `last_name` varchar(45) NOT NULL,\n  `last_update`\
  \ timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY\
  \ KEY (`actor_id`),\n  KEY `idx_actor_last_name` (`last_name`)\n) ENGINE=InnoDB\
  \ AUTO_INCREMENT=201 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
actor_info: 'CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY INVOKER
  VIEW `actor_info` AS select `a`.`actor_id` AS `actor_id`,`a`.`first_name` AS `first_name`,`a`.`last_name`
  AS `last_name`,group_concat(distinct concat(`c`.`name`,'': '',(select group_concat(`f`.`title`
  order by `f`.`title` ASC separator '', '') from ((`film` `f` join `film_category`
  `fc` on((`f`.`film_id` = `fc`.`film_id`))) join `film_actor` `fa` on((`f`.`film_id`
  = `fa`.`film_id`))) where ((`fc`.`category_id` = `c`.`category_id`) and (`fa`.`actor_id`
  = `a`.`actor_id`)))) order by `c`.`name` ASC separator ''; '') AS `film_info` from
  (((`actor` `a` left join `film_actor` `fa` on((`a`.`actor_id` = `fa`.`actor_id`)))
  left join `film_category` `fc` on((`fa`.`film_id` = `fc`.`film_id`))) left join
  `category` `c` on((`fc`.`category_id` = `c`.`category_id`))) group by `a`.`actor_id`,`a`.`first_name`,`a`.`last_name`'
address: "CREATE TABLE `address` (\n  `address_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `address` varchar(50) NOT NULL,\n  `address2` varchar(50) DEFAULT NULL,\n  `district`\
  \ varchar(20) NOT NULL,\n  `city_id` smallint unsigned NOT NULL,\n  `postal_code`\
  \ varchar(10) DEFAULT NULL,\n  `phone` varchar(20) NOT NULL,\n  `location` geometry\
  \ NOT NULL /*!80003 SRID 0 */,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP\
  \ ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`address_id`),\n  KEY `idx_fk_city_id`\
  \ (`city_id`),\n  SPATIAL KEY `idx_location` (`location`),\n  CONSTRAINT `fk_address_city`\
  \ FOREIGN KEY (`city_id`) REFERENCES `city` (`city_id`) ON DELETE RESTRICT ON UPDATE\
  \ CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=606 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
category: "CREATE TABLE `category` (\n  `category_id` tinyint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `name` varchar(25) NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP\
  \ ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`category_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=17\
  \ DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
city: "CREATE TABLE `city` (\n  `city_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `city` varchar(50) NOT NULL,\n  `country_id` smallint unsigned NOT NULL,\n  `last_update`\
  \ timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY\
  \ KEY (`city_id`),\n  KEY `idx_fk_country_id` (`country_id`),\n  CONSTRAINT `fk_city_country`\
  \ FOREIGN KEY (`country_id`) REFERENCES `country` (`country_id`) ON DELETE RESTRICT\
  \ ON UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=601 DEFAULT CHARSET=utf8mb4\
  \ COLLATE=utf8mb4_0900_ai_ci"
country: "CREATE TABLE `country` (\n  `country_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `country` varchar(50) NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP\
  \ ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`country_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=110\
  \ DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
customer: "CREATE TABLE `customer` (\n  `customer_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `store_id` tinyint unsigned NOT NULL,\n  `first_name` varchar(45) NOT NULL,\n\
  \  `last_name` varchar(45) NOT NULL,\n  `email` varchar(50) DEFAULT NULL,\n  `address_id`\
  \ smallint unsigned NOT NULL,\n  `active` tinyint(1) NOT NULL DEFAULT '1',\n  `create_date`\
  \ datetime NOT NULL,\n  `last_update` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON\
  \ UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`customer_id`),\n  KEY `idx_fk_store_id`\
  \ (`store_id`),\n  KEY `idx_fk_address_id` (`address_id`),\n  KEY `idx_last_name`\
  \ (`last_name`),\n  CONSTRAINT `fk_customer_address` FOREIGN KEY (`address_id`)\
  \ REFERENCES `address` (`address_id`) ON DELETE RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT\
  \ `fk_customer_store` FOREIGN KEY (`store_id`) REFERENCES `store` (`store_id`) ON\
  \ DELETE RESTRICT ON UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=600 DEFAULT\
  \ CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
customer_list: CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY
  DEFINER VIEW `customer_list` AS select `cu`.`customer_id` AS `ID`,concat(`cu`.`first_name`,_utf8mb4'
  ',`cu`.`last_name`) AS `name`,`a`.`address` AS `address`,`a`.`postal_code` AS `zip
  code`,`a`.`phone` AS `phone`,`city`.`city` AS `city`,`country`.`country` AS `country`,if(`cu`.`active`,_utf8mb4'active',_utf8mb4'')
  AS `notes`,`cu`.`store_id` AS `SID` from (((`customer` `cu` join `address` `a` on((`cu`.`address_id`
  = `a`.`address_id`))) join `city` on((`a`.`city_id` = `city`.`city_id`))) join `country`
  on((`city`.`country_id` = `country`.`country_id`)))
film: "CREATE TABLE `film` (\n  `film_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `title` varchar(128) NOT NULL,\n  `description` text,\n  `release_year` year\
  \ DEFAULT NULL,\n  `language_id` tinyint unsigned NOT NULL,\n  `original_language_id`\
  \ tinyint unsigned DEFAULT NULL,\n  `rental_duration` tinyint unsigned NOT NULL\
  \ DEFAULT '3',\n  `rental_rate` decimal(4,2) NOT NULL DEFAULT '4.99',\n  `length`\
  \ smallint unsigned DEFAULT NULL,\n  `replacement_cost` decimal(5,2) NOT NULL DEFAULT\
  \ '19.99',\n  `rating` enum('G','PG','PG-13','R','NC-17') DEFAULT 'G',\n  `special_features`\
  \ set('Trailers','Commentaries','Deleted Scenes','Behind the Scenes') DEFAULT NULL,\n\
  \  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n\
  \  PRIMARY KEY (`film_id`),\n  KEY `idx_title` (`title`),\n  KEY `idx_fk_language_id`\
  \ (`language_id`),\n  KEY `idx_fk_original_language_id` (`original_language_id`),\n\
  \  CONSTRAINT `fk_film_language` FOREIGN KEY (`language_id`) REFERENCES `language`\
  \ (`language_id`) ON DELETE RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT `fk_film_language_original`\
  \ FOREIGN KEY (`original_language_id`) REFERENCES `language` (`language_id`) ON\
  \ DELETE RESTRICT ON UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=1001 DEFAULT\
  \ CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
film_actor: "CREATE TABLE `film_actor` (\n  `actor_id` smallint unsigned NOT NULL,\n\
  \  `film_id` smallint unsigned NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT\
  \ CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`actor_id`,`film_id`),\n\
  \  KEY `idx_fk_film_id` (`film_id`),\n  CONSTRAINT `fk_film_actor_actor` FOREIGN\
  \ KEY (`actor_id`) REFERENCES `actor` (`actor_id`) ON DELETE RESTRICT ON UPDATE\
  \ CASCADE,\n  CONSTRAINT `fk_film_actor_film` FOREIGN KEY (`film_id`) REFERENCES\
  \ `film` (`film_id`) ON DELETE RESTRICT ON UPDATE CASCADE\n) ENGINE=InnoDB DEFAULT\
  \ CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
film_category: "CREATE TABLE `film_category` (\n  `film_id` smallint unsigned NOT\
  \ NULL,\n  `category_id` tinyint unsigned NOT NULL,\n  `last_update` timestamp NOT\
  \ NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`film_id`,`category_id`),\n\
  \  KEY `fk_film_category_category` (`category_id`),\n  CONSTRAINT `fk_film_category_category`\
  \ FOREIGN KEY (`category_id`) REFERENCES `category` (`category_id`) ON DELETE RESTRICT\
  \ ON UPDATE CASCADE,\n  CONSTRAINT `fk_film_category_film` FOREIGN KEY (`film_id`)\
  \ REFERENCES `film` (`film_id`) ON DELETE RESTRICT ON UPDATE CASCADE\n) ENGINE=InnoDB\
  \ DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
film_list: CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER
  VIEW `film_list` AS select `film`.`film_id` AS `FID`,`film`.`title` AS `title`,`film`.`description`
  AS `description`,`category`.`name` AS `category`,`film`.`rental_rate` AS `price`,`film`.`length`
  AS `length`,`film`.`rating` AS `rating`,group_concat(concat(`actor`.`first_name`,_utf8mb4'
  ',`actor`.`last_name`) separator ', ') AS `actors` from ((((`film` left join `film_category`
  on((`film_category`.`film_id` = `film`.`film_id`))) left join `category` on((`category`.`category_id`
  = `film_category`.`category_id`))) left join `film_actor` on((`film`.`film_id` =
  `film_actor`.`film_id`))) left join `actor` on((`film_actor`.`actor_id` = `actor`.`actor_id`)))
  group by `film`.`film_id`,`category`.`name`
film_text: "CREATE TABLE `film_text` (\n  `film_id` smallint unsigned NOT NULL,\n\
  \  `title` varchar(255) NOT NULL,\n  `description` text,\n  PRIMARY KEY (`film_id`),\n\
  \  FULLTEXT KEY `idx_title_description` (`title`,`description`)\n) ENGINE=InnoDB\
  \ DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
inventory: "CREATE TABLE `inventory` (\n  `inventory_id` mediumint unsigned NOT NULL\
  \ AUTO_INCREMENT,\n  `film_id` smallint unsigned NOT NULL,\n  `store_id` tinyint\
  \ unsigned NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP\
  \ ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`inventory_id`),\n  KEY `idx_fk_film_id`\
  \ (`film_id`),\n  KEY `idx_store_id_film_id` (`store_id`,`film_id`),\n  CONSTRAINT\
  \ `fk_inventory_film` FOREIGN KEY (`film_id`) REFERENCES `film` (`film_id`) ON DELETE\
  \ RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT `fk_inventory_store` FOREIGN KEY (`store_id`)\
  \ REFERENCES `store` (`store_id`) ON DELETE RESTRICT ON UPDATE CASCADE\n) ENGINE=InnoDB\
  \ AUTO_INCREMENT=4582 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
language: "CREATE TABLE `language` (\n  `language_id` tinyint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `name` char(20) NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP\
  \ ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`language_id`)\n) ENGINE=InnoDB AUTO_INCREMENT=7\
  \ DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
nicer_but_slower_film_list: CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost`
  SQL SECURITY DEFINER VIEW `nicer_but_slower_film_list` AS select `film`.`film_id`
  AS `FID`,`film`.`title` AS `title`,`film`.`description` AS `description`,`category`.`name`
  AS `category`,`film`.`rental_rate` AS `price`,`film`.`length` AS `length`,`film`.`rating`
  AS `rating`,group_concat(concat(concat(upper(substr(`actor`.`first_name`,1,1)),lower(substr(`actor`.`first_name`,2,length(`actor`.`first_name`))),_utf8mb4'
  ',concat(upper(substr(`actor`.`last_name`,1,1)),lower(substr(`actor`.`last_name`,2,length(`actor`.`last_name`))))))
  separator ', ') AS `actors` from ((((`film` left join `film_category` on((`film_category`.`film_id`
  = `film`.`film_id`))) left join `category` on((`category`.`category_id` = `film_category`.`category_id`)))
  left join `film_actor` on((`film`.`film_id` = `film_actor`.`film_id`))) left join
  `actor` on((`film_actor`.`actor_id` = `actor`.`actor_id`))) group by `film`.`film_id`,`category`.`name`
payment: "CREATE TABLE `payment` (\n  `payment_id` smallint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `customer_id` smallint unsigned NOT NULL,\n  `staff_id` tinyint unsigned NOT\
  \ NULL,\n  `rental_id` int DEFAULT NULL,\n  `amount` decimal(5,2) NOT NULL,\n  `payment_date`\
  \ datetime NOT NULL,\n  `last_update` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON\
  \ UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`payment_id`),\n  KEY `idx_fk_staff_id`\
  \ (`staff_id`),\n  KEY `idx_fk_customer_id` (`customer_id`),\n  KEY `fk_payment_rental`\
  \ (`rental_id`),\n  CONSTRAINT `fk_payment_customer` FOREIGN KEY (`customer_id`)\
  \ REFERENCES `customer` (`customer_id`) ON DELETE RESTRICT ON UPDATE CASCADE,\n\
  \  CONSTRAINT `fk_payment_rental` FOREIGN KEY (`rental_id`) REFERENCES `rental`\
  \ (`rental_id`) ON DELETE SET NULL ON UPDATE CASCADE,\n  CONSTRAINT `fk_payment_staff`\
  \ FOREIGN KEY (`staff_id`) REFERENCES `staff` (`staff_id`) ON DELETE RESTRICT ON\
  \ UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=16050 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
rental: "CREATE TABLE `rental` (\n  `rental_id` int NOT NULL AUTO_INCREMENT,\n  `rental_date`\
  \ datetime NOT NULL,\n  `inventory_id` mediumint unsigned NOT NULL,\n  `customer_id`\
  \ smallint unsigned NOT NULL,\n  `return_date` datetime DEFAULT NULL,\n  `staff_id`\
  \ tinyint unsigned NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP\
  \ ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`rental_id`),\n  UNIQUE KEY `rental_date`\
  \ (`rental_date`,`inventory_id`,`customer_id`),\n  KEY `idx_fk_inventory_id` (`inventory_id`),\n\
  \  KEY `idx_fk_customer_id` (`customer_id`),\n  KEY `idx_fk_staff_id` (`staff_id`),\n\
  \  CONSTRAINT `fk_rental_customer` FOREIGN KEY (`customer_id`) REFERENCES `customer`\
  \ (`customer_id`) ON DELETE RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT `fk_rental_inventory`\
  \ FOREIGN KEY (`inventory_id`) REFERENCES `inventory` (`inventory_id`) ON DELETE\
  \ RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT `fk_rental_staff` FOREIGN KEY (`staff_id`)\
  \ REFERENCES `staff` (`staff_id`) ON DELETE RESTRICT ON UPDATE CASCADE\n) ENGINE=InnoDB\
  \ AUTO_INCREMENT=16050 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
sales_by_film_category: CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL
  SECURITY DEFINER VIEW `sales_by_film_category` AS select `c`.`name` AS `category`,sum(`p`.`amount`)
  AS `total_sales` from (((((`payment` `p` join `rental` `r` on((`p`.`rental_id` =
  `r`.`rental_id`))) join `inventory` `i` on((`r`.`inventory_id` = `i`.`inventory_id`)))
  join `film` `f` on((`i`.`film_id` = `f`.`film_id`))) join `film_category` `fc` on((`f`.`film_id`
  = `fc`.`film_id`))) join `category` `c` on((`fc`.`category_id` = `c`.`category_id`)))
  group by `c`.`name` order by `total_sales` desc
sales_by_store: CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY
  DEFINER VIEW `sales_by_store` AS select concat(`c`.`city`,_utf8mb4',',`cy`.`country`)
  AS `store`,concat(`m`.`first_name`,_utf8mb4' ',`m`.`last_name`) AS `manager`,sum(`p`.`amount`)
  AS `total_sales` from (((((((`payment` `p` join `rental` `r` on((`p`.`rental_id`
  = `r`.`rental_id`))) join `inventory` `i` on((`r`.`inventory_id` = `i`.`inventory_id`)))
  join `store` `s` on((`i`.`store_id` = `s`.`store_id`))) join `address` `a` on((`s`.`address_id`
  = `a`.`address_id`))) join `city` `c` on((`a`.`city_id` = `c`.`city_id`))) join
  `country` `cy` on((`c`.`country_id` = `cy`.`country_id`))) join `staff` `m` on((`s`.`manager_staff_id`
  = `m`.`staff_id`))) group by `s`.`store_id` order by `cy`.`country`,`c`.`city`
staff: "CREATE TABLE `staff` (\n  `staff_id` tinyint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `first_name` varchar(45) NOT NULL,\n  `last_name` varchar(45) NOT NULL,\n  `address_id`\
  \ smallint unsigned NOT NULL,\n  `picture` blob,\n  `email` varchar(50) DEFAULT\
  \ NULL,\n  `store_id` tinyint unsigned NOT NULL,\n  `active` tinyint(1) NOT NULL\
  \ DEFAULT '1',\n  `username` varchar(16) NOT NULL,\n  `password` varchar(40) CHARACTER\
  \ SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,\n  `last_update` timestamp NOT NULL\
  \ DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`staff_id`),\n\
  \  KEY `idx_fk_store_id` (`store_id`),\n  KEY `idx_fk_address_id` (`address_id`),\n\
  \  CONSTRAINT `fk_staff_address` FOREIGN KEY (`address_id`) REFERENCES `address`\
  \ (`address_id`) ON DELETE RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT `fk_staff_store`\
  \ FOREIGN KEY (`store_id`) REFERENCES `store` (`store_id`) ON DELETE RESTRICT ON\
  \ UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
staff_list: CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER
  VIEW `staff_list` AS select `s`.`staff_id` AS `ID`,concat(`s`.`first_name`,_utf8mb4'
  ',`s`.`last_name`) AS `name`,`a`.`address` AS `address`,`a`.`postal_code` AS `zip
  code`,`a`.`phone` AS `phone`,`city`.`city` AS `city`,`country`.`country` AS `country`,`s`.`store_id`
  AS `SID` from (((`staff` `s` join `address` `a` on((`s`.`address_id` = `a`.`address_id`)))
  join `city` on((`a`.`city_id` = `city`.`city_id`))) join `country` on((`city`.`country_id`
  = `country`.`country_id`)))
store: "CREATE TABLE `store` (\n  `store_id` tinyint unsigned NOT NULL AUTO_INCREMENT,\n\
  \  `manager_staff_id` tinyint unsigned NOT NULL,\n  `address_id` smallint unsigned\
  \ NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE\
  \ CURRENT_TIMESTAMP,\n  PRIMARY KEY (`store_id`),\n  UNIQUE KEY `idx_unique_manager`\
  \ (`manager_staff_id`),\n  KEY `idx_fk_address_id` (`address_id`),\n  CONSTRAINT\
  \ `fk_store_address` FOREIGN KEY (`address_id`) REFERENCES `address` (`address_id`)\
  \ ON DELETE RESTRICT ON UPDATE CASCADE,\n  CONSTRAINT `fk_store_staff` FOREIGN KEY\
  \ (`manager_staff_id`) REFERENCES `staff` (`staff_id`) ON DELETE RESTRICT ON UPDATE\
  \ CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci"
