# 告诉 pip 在标准 PyPI 之外，也要去这个 URL 寻找包。这行通常放在文件顶部。
--extra-index-url https://download.pytorch.org/whl/cpu
aiofiles==24.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.49.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
argcomplete==1.10.3
asgiref==3.8.1
asttokens==3.0.0
async-timeout==4.0.3
attrs==25.1.0
backoff==2.2.1
bcrypt==4.2.1
beautifulsoup4==4.8.2
build==1.2.2.post1
cachetools==5.5.1
certifi==2025.1.31
cffi==1.17.1
chardet==3.0.4
charset-normalizer==3.4.1
chroma-hnswlib==0.7.6
chromadb==0.6.3
click==8.1.8
cohere==5.13.12
coloredlogs==15.0.1
comm==0.2.2
compressed-rtf==1.0.6
contourpy==1.3.1
cryptography==44.0.0
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.12
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.18
distro==1.9.0
docx2txt==0.8
durationpy==0.9
ebcdic==1.1.1
effdet==0.4.1
emoji==2.14.1
eval_type_backport==0.2.2
exceptiongroup==1.2.2
executing==2.2.0
extract-msg==0.28.7
faiss-cpu==1.10.0
fastapi==0.115.8
fastavro==1.10.0
filelock==3.17.0
filetype==1.2.0
flatbuffers==25.1.24
fonttools==4.55.8
frozenlist==1.5.0
fsspec==2025.2.0
google-api-core==2.24.1
google-auth==2.38.0
google-cloud-vision==3.9.0
googleapis-common-protos==1.66.0
greenlet==3.1.1
grpcio==1.67.1
grpcio-status==1.67.1
h11==0.14.0
html5lib==1.1
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.28.1
humanfriendly==10.0
idna==3.10
IMAPClient==2.1.0
importlib_metadata==8.5.0
importlib_resources==6.5.2
ipykernel==6.29.5
ipython==8.32.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
jq==1.8.0
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==3.0.0
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.8
kubernetes==32.0.0
langchain==0.3.17
langchain-chroma==0.2.1
langchain-cohere==0.4.2
langchain-community==0.3.16
langchain-core==0.3.33
langchain-deepseek-official==0.1.0
langchain-huggingface==0.1.2
langchain-milvus==0.1.8
langchain-openai==0.3.3
langchain-text-splitters==0.3.5
langchain-unstructured==0.1.6
langdetect==1.0.9
langgraph==0.2.69
langgraph-checkpoint==2.0.10
langgraph-sdk==0.1.51
langsmith==0.3.4
lark==1.2.2
llvmlite==0.44.0
lxml==5.3.0
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.0
matplotlib==3.10.0
matplotlib-inline==0.1.7
mdurl==0.1.2
milvus-lite==2.4.11
mmh3==5.1.0
monotonic==1.6
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numba==0.61.0
numpy==1.26.4
oauthlib==3.2.2
olefile==0.47
omegaconf==2.3.0
onnx==1.17.0
onnxruntime==1.19.2
openai==1.61.0
opencv-python==4.11.0.86
opentelemetry-api==1.30.0
opentelemetry-exporter-otlp-proto-common==1.30.0
opentelemetry-exporter-otlp-proto-grpc==1.30.0
opentelemetry-instrumentation==0.51b0
opentelemetry-instrumentation-asgi==0.51b0
opentelemetry-instrumentation-fastapi==0.51b0
opentelemetry-proto==1.30.0
opentelemetry-sdk==1.30.0
opentelemetry-semantic-conventions==0.51b0
opentelemetry-util-http==0.51b0
orjson==3.10.15
overrides==7.7.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pdf2image==1.17.0
pdfminer.six==20240706
pexpect==4.9.0
pi_heif==0.21.0
pikepdf==9.5.1
pillow==11.1.0
platformdirs==4.3.6
posthog==3.11.0
prompt_toolkit==3.0.50
propcache==0.2.1
proto-plus==1.26.0
protobuf==5.29.3
psutil==6.1.1
ptyprocess==0.7.0
pure_eval==0.2.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycocotools==2.0.8
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.6
pydantic-settings==2.7.1
pydantic_core==2.27.2
Pygments==2.19.1
pymilvus==2.5.4
pymilvus.model==0.3.2
PyMuPDF==1.25.2
pynndescent==0.5.13
pyparsing==3.2.1
pypdf==5.2.0
pypdfium2==4.30.1
PyPika==0.48.9
pyproject_hooks==1.2.0
pytesseract==0.3.13
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-iso639==2025.1.28
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
python-pptx==0.6.23
pytube==15.0.0
pytz==2025.1
PyYAML==6.0.2
pyzmq==26.2.1
rank-llm==0.21.0
rank-bm25==0.2.2
RapidFuzz==3.12.1
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rsa==4.9
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.1
sentence-transformers==3.4.1
shellingham==1.5.4
six==1.12.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
SpeechRecognition==3.8.1
SQLAlchemy==2.0.37
stack-data==0.6.3
starlette==0.45.3
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.8.0
timm==1.0.14
tokenizers==0.21.0
tomli==2.2.1
# 带有 +cpu 后缀的版本通常是发布在PyTorch自己的软件包索引上的，而不是 PyPI 官方源。
# pip3 install torch==2.6.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
torch==2.6.0+cpu
torchvision==0.21.0+cpu
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.48.2
triton==3.2.0
typer==0.15.1
types-PyYAML==6.0.12.20241230
types-requests==2.32.0.20241016
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
tzlocal==5.2
ujson==5.10.0
umap-learn==0.5.7
unstructured==0.16.17
unstructured-client==0.29.0
unstructured-inference==0.8.7
unstructured.pytesseract==0.3.13
urllib3==2.3.0
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.4
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
websockets==14.2
wrapt==1.17.2
xlrd==1.2.0
XlsxWriter==3.2.2
yarl==1.18.3
youtube-transcript-api==0.6.3
zipp==3.21.0
zstandard==0.23.0
milvus-model==0.2.12
llmlingua==0.2.2
FlagEmbedding==1.3.5
ragas==0.2.15
trulens==1.5.1
trulens-providers-openai==1.5.1
deepeval==3.0.8
weaviate==0.1.2
weaviate-client==4.10.4