{"cells": [{"cell_type": "markdown", "id": "42a571b8-51c4-43c5-883f-d2e199a41131", "metadata": {}, "source": ["## 用LangChain加载文档并创建Documents\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d747a71d-ee86-4568-ab55-de499c557d5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'source': '/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑神话悟空的设定.txt'}, page_content='《黑神话：悟空》的故事可分为六个章节，名为“火照黑云”、“风起黄昏”、“夜生白露”、“曲度紫鸳”、“日落红尘”和“未竟”，并且拥有两个结局，玩家的选择和经历将影响最终的结局。\\n每个章节结尾，附有二维和三维的动画过场，展示和探索《黑神话：悟空》中的叙事和主题元素。\\n游戏的设定融合了中国的文化和自然地标。例如重庆的大足石刻、山西省的小西天、南禅寺、铁佛寺、广胜寺和鹳雀楼等，都在游戏中出现。游戏也融入了佛教和道教的哲学元素。')]\n"]}], "source": ["from langchain_community.document_loaders import TextLoader\n", "loader = TextLoader(\"/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑神话悟空的设定.txt\")\n", "documents = loader.load()\n", "print(documents)"]}, {"cell_type": "markdown", "id": "da33086c-f294-4a14-8e08-0f4be401bab8", "metadata": {}, "source": ["## 创建Document对象"]}, {"cell_type": "code", "execution_count": 2, "id": "4fc2bfe1-e44f-454c-af42-79a23b329a9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'source': '师徒四人.txt'}, page_content='悟空是大师兄.'), Document(metadata={'source': '师徒四人.txt '}, page_content='八戒是二师兄.')]\n"]}], "source": ["from langchain_core.documents import Document\n", "documents = [\n", "    Document(\n", "        page_content=\"悟空是大师兄.\",\n", "        metadata={\"source\": \"师徒四人.txt\"},\n", "    ),\n", "    Document(\n", "        page_content=\"八戒是二师兄.\",\n", "        metadata={\"source\": \"师徒四人.txt \"},\n", "    ),\n", "]\n", "print(documents)"]}, {"cell_type": "markdown", "id": "2fe39431-9038-4643-a2e1-6058b6cc9450", "metadata": {}, "source": ["## 加载目录中所有文档"]}, {"cell_type": "code", "execution_count": 3, "id": "e243f216-3cc5-40fc-a75b-e7fa8d0bd245", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/mnt/external_disk/venv/20250203_<PERSON><PERSON><PERSON><PERSON>/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["文档数：7\n", "page_content=',\n", "\n", "Pons\n", "\n", "= ens eens WUKONGY\n", "\n", "4' metadata={'source': '/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑悟空英文.jpg'}\n"]}], "source": ["from langchain_community.document_loaders import DirectoryLoader\n", "loader = DirectoryLoader(\"/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空\")\n", "docs = loader.load()\n", "print(f\"文档数：{len(docs)}\")  # 输出文档总数\n", "print(docs[0])  # 输出第一个文档"]}, {"cell_type": "markdown", "id": "f0aa7182-8593-4e78-9532-a50374a4f88c", "metadata": {}, "source": ["## 指定加载参数"]}, {"cell_type": "code", "execution_count": 4, "id": "c0a3b3a1-a60f-44f6-9c65-fd8fd86dece3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████| 7/7 [00:01<00:00,  4.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["文档数：7\n", "page_content='Category Name Description PowerLevel 装备 铜云棒 一根结实的青铜棒，挥舞时能发出破空之声，适合近战攻击。 85 装备 百戏衬钱衣 一件精美的战斗铠甲，能够提供强大的防御并抵御剧毒伤害。 90 技能 天雷击 召唤天雷攻击敌人，造成大范围雷电伤害。 95 技能 火焰舞 施展火焰舞步，将敌人包围在炽热的火焰之中。 92 人物 悟空 主角，拥有七十二变和腾云驾雾的能力，行侠仗义。 100 人物 银角大王 强大的妖王之一，擅长操控各种法宝，具有极高的战斗力。 88' metadata={'source': '/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑神话悟空.csv'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from langchain_community.document_loaders import DirectoryLoader\n", "loader = DirectoryLoader(\"/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空\", \n", "                         # glob=\"**/*.md\", \n", "                         use_multithreading=True,\n", "                         show_progress=True,\n", "                         )\n", "docs = loader.load()\n", "print(f\"文档数：{len(docs)}\")  # 输出文档总数\n", "print(docs[0])  # 输出第一个文档"]}, {"cell_type": "markdown", "id": "9dd762bb-b4ff-4996-963b-7088647dd8c5", "metadata": {}, "source": ["## 更改加载工具"]}, {"cell_type": "code", "execution_count": 5, "id": "b55be60e-f43d-48fc-a5b4-b691af9e444e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 黑神话：悟空 🐵\n", "\n", "> 黑神话：悟空 是由中国游戏开发团队制作的一款备受瞩目的动作冒险游戏，以《西游记》为背景，重新演绎了经典故事，带来了极具冲击力的视觉和游戏体验。\n", "\n", "## 游戏版本介绍\n", "\n", "##\n"]}], "source": ["from langchain_community.document_loaders import DirectoryLoader, TextLoader\n", "# 加载目录下所有 Markdown 文件\n", "loader = DirectoryLoader(\"/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空\",\n", "                         glob=\"**/*.md\",\n", "                         loader_cls=TextLoader)\n", "docs = loader.load()\n", "print(docs[0].page_content[:100])  # 打印第一个文档内容的前100个字符"]}, {"cell_type": "markdown", "id": "ad3e1101-e99b-4a16-a4a1-a5ebc36fdb82", "metadata": {}, "source": ["## 跳过出错文件"]}, {"cell_type": "code", "execution_count": 6, "id": "bdf185f6-c435-4965-b3a0-ceb5baaea3de", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error loading file /home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑悟空英文.jpg: Error loading /home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑悟空英文.jpg\n", "Error loading file /home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑悟空销量.jpg: Error loading /home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑悟空销量.jpg\n", "Error loading file /home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑神话悟空.pdf: Error loading /home/<USER>/Documents/20250202_RAG_Book/data/黑悟空/黑神话悟空.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["﻿Category,Name,Description,PowerLevel\n", "装备,铜云棒,一根结实的青铜棒，挥舞时能发出破空之声，适合近战攻击。,85\n", "装备,百戏衬钱衣,一件精美的战斗铠甲，能够提供强\n"]}], "source": ["from langchain_community.document_loaders import DirectoryLoader, TextLoader\n", "# 加载目录下所有文件，跳过出错文件，因为有些文件是图片，TextLoader 无法加载\n", "loader = DirectoryLoader(\"/home/<USER>/Documents/20250202_RAG_Book/data/黑悟空\",\n", "                          silent_errors=True,\n", "                         loader_cls=TextLoader)\n", "\n", "docs = loader.load()\n", "print(docs[0].page_content[:100])  # 打印第一个文档内容的前100个字符"]}, {"cell_type": "code", "execution_count": null, "id": "60007611-b267-4596-adb0-7c193c68d43d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (20250203_LangChain)", "language": "python", "name": "20250203_langchain"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}