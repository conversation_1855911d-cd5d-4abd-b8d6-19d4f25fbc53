aiofiles==24.1.0
aiohappyeyeballs==2.5.0
aiohttp==3.11.13
aiosignal==1.3.2
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.8.0
async-timeout==5.0.1
attrs==25.1.0
azure-core==1.32.0
azure-identity==1.20.0
backoff==2.2.1
beautifulsoup4==4.13.3
cachetools==5.5.2
camelot-py==1.0.0
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
click==8.1.8
cohere==5.14.0
coloredlogs==15.0.1
contourpy==1.3.1
cryptography==44.0.2
cycler==0.12.1
dataclasses-json==0.6.7
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
duckdb==1.2.1
effdet==0.4.1
emoji==2.14.1
et_xmlfile==2.0.0
eval_type_backport==0.2.2
exceptiongroup==1.2.2
fastavro==1.10.0
filelock==3.17.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2025.3.0
ghostscript==0.7
google-api-core==2.24.2
google-auth==2.38.0
google-cloud-vision==3.10.0
googleapis-common-protos==1.69.1
greenlet==3.1.1
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
html5lib==1.1
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.2
humanfriendly==10.0
idna==3.10
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonpath-python==1.0.6
kiwisolver==1.4.8
langdetect==1.0.9
llama-cloud==0.1.14
llama-cloud-services==0.6.5
llama-index==0.12.23
llama-index-agent-openai==0.4.6
llama-index-cli==0.4.1
llama-index-core==0.12.23.post2
llama-index-embeddings-adapter==0.3.0
llama-index-embeddings-openai==0.3.1
llama-index-experimental==0.5.4
llama-index-finetuning==0.3.0
llama-index-indices-managed-llama-cloud==0.6.8
llama-index-llms-azure-openai==0.3.2
llama-index-llms-mistralai==0.3.3
llama-index-llms-openai==0.3.25
llama-index-multi-modal-llms-openai==0.4.3
llama-index-postprocessor-cohere-rerank==0.3.0
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.6
llama-index-readers-llama-parse==0.4.0
llama-parse==0.6.4.post1
lxml==5.3.1
Markdown==3.7
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.1
mistralai==1.5.1
mpmath==1.3.0
msal==1.31.1
msal-extensions==1.2.0
multidict==6.1.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==1.26.4
olefile==0.47
omegaconf==2.3.0
onnx==1.17.0
onnxruntime==1.21.0
openai==1.65.5
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
packaging==24.2
pandas==2.2.3
pdf2image==1.17.0
pdfminer.six==20240706
pi_heif==0.21.0
pikepdf==9.5.2
pillow==11.1.0
portalocker==2.10.1
propcache==0.3.0
proto-plus==1.26.1
protobuf==5.29.3
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycocotools==2.0.8
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
PyJWT==2.10.1
PyMuPDF==1.25.3
pypandoc==1.15
pyparsing==3.2.1
pypdf==5.3.1
pypdfium2==4.30.1
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-iso639==2025.2.18
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
python-pptx==1.0.2
pytz==2025.1
PyYAML==6.0.2
RapidFuzz==3.12.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rsa==4.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==3.4.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.38
striprtf==0.0.26
sympy==1.13.1
tabulate==0.9.0
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.9.0
timm==1.0.15
tokenizers==0.21.0
torch==2.6.0
torchvision==0.21.0
tqdm==4.67.1
transformers==4.49.0
types-requests==2.32.0.20250306
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2025.1
unstructured==0.16.25
unstructured-client==0.31.1
unstructured-inference==0.8.9
unstructured.pytesseract==0.3.15
urllib3==2.3.0
webencodings==0.5.1
wrapt==1.17.2
xlrd==2.0.1
XlsxWriter==3.2.2
yarl==1.18.3
