aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.1.0
Authlib==1.3.1
beautifulsoup4==4.12.3
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
comm==0.2.2
cryptography==44.0.0
dataclasses-json==0.6.7
debugpy==1.8.12
decorator==5.1.1
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
exceptiongroup==1.2.2
executing==2.2.0
filelock==3.17.0
filetype==1.2.0
frozenlist==1.5.0
fsspec==2025.2.0
greenlet==3.1.1
grpcio==1.70.0
grpcio-health-checking==1.70.0
grpcio-tools==1.70.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.28.1
idna==3.10
ipykernel==6.29.5
ipython==8.32.0
jedi==0.19.2
Jinja2==3.1.5
jiter==0.8.2
joblib==1.4.2
jupyter_client==8.6.3
jupyter_core==5.7.2
llama-cloud==0.1.11
llama-index==0.12.15
llama-index-agent-openai==0.4.3
llama-index-cli==0.4.0
llama-index-core==0.12.15
llama-index-embeddings-huggingface==0.5.1
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.4
llama-index-llms-deepseek==0.1.1
llama-index-llms-openai==0.3.16
llama-index-llms-openai-like==0.3.3
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-database==0.3.0
llama-index-readers-file==0.4.4
llama-index-readers-github==0.5.0
llama-index-readers-llama-parse==0.4.0
llama-index-vector-stores-weaviate==1.3.1
llama-parse==0.5.20
MarkupSafe==3.0.2
marshmallow==3.26.0
matplotlib-inline==0.1.7
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
mysqlclient==2.2.7
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.2
openai==1.61.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pexpect==4.9.0
pillow==11.1.0
platformdirs==4.3.6
prompt_toolkit==3.0.50
propcache==0.2.1
protobuf==5.29.3
psutil==6.1.1
ptyprocess==0.7.0
pure_eval==0.2.3
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
Pygments==2.19.1
PyMuPDF==1.25.2
pypdf==5.2.0
python-dateutil==2.9.0.post0
pytz==2025.1
PyYAML==6.0.2
pyzmq==26.2.1
regex==2024.11.6
requests==2.32.3
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.1
sentence-transformers==3.4.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.37
stack-data==0.6.3
striprtf==0.0.26
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.8.0
tokenizers==0.21.0
torch==2.6.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.48.2
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
validators==0.34.0
wcwidth==0.2.13
weaviate-client==4.10.4
wrapt==1.17.2
yarl==1.18.3 