{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5cb73c23-cfef-4263-be88-25fec5c32042", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/mnt/external_disk/venv/20250202_LlamaIndex/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from llama_index.llms.openai import OpenAI\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from llama_index.embeddings.huggingface import HuggingFaceEmbedding\n", "from llama_index.core.node_parser import SentenceWindowNodeParser\n", "from llama_index.core.node_parser import SentenceSplitter\n", "\n", "# create the sentence window node parser w/ default settings\n", "node_parser = SentenceWindowNodeParser.from_defaults(\n", "    window_size=3,\n", "    window_metadata_key=\"window\",\n", "    original_text_metadata_key=\"original_text\",\n", ")\n", "\n", "# base node parser is a sentence splitter\n", "text_splitter = SentenceSplitter()\n", "\n", "llm = OpenAI(model=\"gpt-3.5-turbo\", temperature=0.1)\n", "embed_model = HuggingFaceEmbedding(\n", "    model_name=\"sentence-transformers/all-mpnet-base-v2\", max_length=512\n", ")\n", "\n", "from llama_index.core import Settings\n", "\n", "Settings.llm = llm\n", "Settings.embed_model = embed_model\n", "Settings.text_splitter = text_splitter"]}, {"cell_type": "code", "execution_count": 3, "id": "5b317db2-f4d6-4c73-9911-36d501e53b62", "metadata": {}, "outputs": [], "source": ["from llama_index.core import SimpleDirectoryReader\n", "\n", "documents = SimpleDirectoryReader(\n", "    input_files=[\"/home/<USER>/Documents/rag-in-action/90-文档-Data/复杂PDF/IPCC_AR6_WGII_Chapter03.pdf\"]\n", ").load_data()"]}, {"cell_type": "code", "execution_count": 4, "id": "11fcfa83-0ba3-4348-880b-e843970ea6d7", "metadata": {}, "outputs": [], "source": ["nodes = node_parser.get_nodes_from_documents(documents)"]}, {"cell_type": "code", "execution_count": 5, "id": "d76725bf-2c8c-49cf-affa-0805778c8a5b", "metadata": {}, "outputs": [], "source": ["base_nodes = text_splitter.get_nodes_from_documents(documents)"]}, {"cell_type": "code", "execution_count": 6, "id": "3523596f-4f73-4353-a9f7-c2e9957797de", "metadata": {}, "outputs": [], "source": ["from llama_index.core import VectorStoreIndex\n", "\n", "sentence_index = VectorStoreIndex(nodes)"]}, {"cell_type": "code", "execution_count": 7, "id": "602e12e9-d25d-4aed-b9da-bdef38e2b27e", "metadata": {}, "outputs": [], "source": ["base_index = VectorStoreIndex(base_nodes)"]}, {"cell_type": "code", "execution_count": 8, "id": "198c750b-75f3-4331-8359-f280b331b690", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There is low confidence in the quantification of AMOC changes in the 20th century due to low agreement in quantitative reconstructed and simulated trends. Additionally, direct observational records since the mid-2000s are considered too short to determine the relative contributions of internal variability, natural forcing, and anthropogenic forcing to AMOC change. Despite these uncertainties, it is very likely that the AMOC will decline over the 21st century for all Shared Socioeconomic Pathways (SSP) scenarios, but it is not expected to involve an abrupt collapse before 2100.\n"]}], "source": ["from llama_index.core.postprocessor import MetadataReplacementPostProcessor\n", "\n", "query_engine = sentence_index.as_query_engine(\n", "    similarity_top_k=2,\n", "    # the target key defaults to `window` to match the node_parser's default\n", "    node_postprocessors=[\n", "        MetadataReplacementPostProcessor(target_metadata_key=\"window\")\n", "    ],\n", ")\n", "window_response = query_engine.query(\n", "    \"What are the concerns surrounding the AMOC?\"\n", ")\n", "print(window_response)"]}, {"cell_type": "code", "execution_count": 9, "id": "986f2277-f813-4d0d-b683-63b506af8446", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Window: 4.3.2.2, 9.6.3 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON> et al., \n", "2021)\n", "Extreme sea levels\n", "Relative sea level rise is driving a global increase \n", "in the frequency of extreme sea levels (high \n", "confidence).\n", " 9.6.4 (<PERSON> et al., \n", "2021)\n", "Rising mean relative sea level will continue to \n", "drive an increase in the frequency of extreme sea \n", "levels (high confidence).\n", " 9.6.4 (<PERSON> et al., \n", "2021)\n", "Ocean circulation\n", "Ocean stratification\n", "‘The upper ocean has become more stably \n", "stratified since at least 1970 […] (virtually \n", "certain).’\n", "******* (<PERSON> et al., \n", "2021)\n", "‘Upper-ocean stratification will continue to \n", "increase throughout the 21st century (virtually \n", "certain).’\n", "******* (<PERSON> et al., \n", "2021)\n", "Eastern boundary \n", "upwelling systems\n", "‘Only the California current system \n", "has experienced some large-scale \n", "upwelling-favourable wind intensification since \n", "the 1980s (medium confidence).’\n", "9.2.5 (<PERSON> et al., \n", "2021)\n", "‘Eastern boundary upwelling systems will \n", "change, with a dipole spatial pattern within \n", "each system of reduction at low latitude and \n", "enhancement at high latitude (high confidence).’\n", "9.2.5 (<PERSON> et al., \n", "2021)\n", "Atlantic overturning \n", "circulation (AMOC)\n", "There is low confidence in reconstructed and \n", "modelled AMOC changes for the 20th century.\n", " *******, 9.2.3 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON><PERSON><PERSON> et al., \n", "2021)\n", "The AMOC will decline over the 21st century \n", "(high confidence, but low confidence for \n", "quantitative projections).\n", " *******, 9.2.3 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON> et al., \n", "2021)\n", "Sea ice\n", "Arctic sea ice \n", "changes\n", "‘Current Arctic sea ice coverage levels are the \n", "lowest since at least 1850 for both annual mean \n", "and late-summer values (high confidence).’\n", "*******, 9.3.1 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON><PERSON><PERSON> et al., \n", "2021)\n", "‘The Arctic will become practically ice-free in \n", "September by the end of the 21st century under \n", "SSP2-4.5, SSP3-7.0 and SSP5-8.5[…](high \n", "confidence).’\n", "4.3.2.1, 9.3.1 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON> et al., \n", "2021)\n", "Antarctic sea ice \n", "changes\n", "There is no global significant trend in \n", "Antarctic sea ice area from 1979 to 2020 (high \n", "confidence).\n", " *******, 9.3.2 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON><PERSON><PERSON> et al., \n", "2021)\n", "There is low confidence in model simulations of \n", "future Antarctic sea ice.\n", " 9.3.2 (<PERSON> et al., \n", "2021)\n", "Ocean chemistry\n", "Changes in salinity\n", "The ‘large-scale, near-surface salinity contrasts \n", "have intensified since at least 1950 […] \n", "(virtually certain).’\n", "2.3.3.2, 9.2.2.2 \n", "(<PERSON> et al., 2021; \n", "<PERSON><PERSON><PERSON> et al., 2021)\n", "‘Fresh ocean regions will continue to get fresher \n", "and salty ocean regions will continue to get \n", "saltier in the 21st century (medium confidence).’\n", "9.2.2.2 (<PERSON> et al., \n", "2021)\n", "Ocean acidification Ocean surface pH has declined globally over the \n", "past four decades (virtually certain).\n", "\n", "------------------\n", "Original Sentence: *******, 9.2.3 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON><PERSON><PERSON> et al., \n", "2021)\n", "The AMOC will decline over the 21st century \n", "(high confidence, but low confidence for \n", "quantitative projections).\n", "\n"]}], "source": ["window = window_response.source_nodes[0].node.metadata[\"window\"]\n", "sentence = window_response.source_nodes[0].node.metadata[\"original_text\"]\n", "\n", "print(f\"Window: {window}\")\n", "print(\"------------------\")\n", "print(f\"Original Sentence: {sentence}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "218e7bfb-6bad-4865-8533-69bdb8f09467", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The concerns surrounding the AMOC include potential slowdown or collapse due to climate change, which could have significant impacts on regional and global climate patterns, including sea level rise, temperature changes, and extreme weather events.\n"]}], "source": ["query_engine = base_index.as_query_engine(similarity_top_k=2)\n", "vector_response = query_engine.query(\n", "    \"What are the concerns surrounding the AMOC?\"\n", ")\n", "print(vector_response)"]}, {"cell_type": "code", "execution_count": 11, "id": "96b263bf-5c06-44b2-b51f-44536cd55cb1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Concerns surrounding the AMOC include potential slowdown or collapse due to climate change, which could lead to significant impacts on regional and global climate patterns, including changes in temperature, precipitation, and sea level rise.\n"]}], "source": ["query_engine = base_index.as_query_engine(similarity_top_k=5)\n", "vector_response = query_engine.query(\n", "    \"What are the concerns surrounding the AMOC?\"\n", ")\n", "print(vector_response)"]}, {"cell_type": "code", "execution_count": 12, "id": "9eef80a3-32d3-423e-b8ee-906169a16c21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*******, 9.2.3 (<PERSON><PERSON><PERSON><PERSON> \n", "et al., 2021; <PERSON><PERSON><PERSON> et al., \n", "2021)\n", "The AMOC will decline over the 21st century \n", "(high confidence, but low confidence for \n", "quantitative projections).\n", "\n", "--------\n", "Over the 21st century, AMOC will very likely decline for all SSP \n", "scenarios but will not involve an abrupt collapse before 2100 (WGI \n", "AR6 Sections 4.3.2, *******; <PERSON> et al., 2021; <PERSON> et al., 2021).\n", "\n", "--------\n"]}], "source": ["for source_node in window_response.source_nodes:\n", "    print(source_node.node.metadata[\"original_text\"])\n", "    print(\"--------\")"]}, {"cell_type": "code", "execution_count": 13, "id": "a9005a96-e162-45b4-b7bb-72b464f25e49", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AMOC mentioned? False\n", "--------\n", "AMOC mentioned? False\n", "--------\n", "AMOC mentioned? False\n", "--------\n", "AMOC mentioned? False\n", "--------\n", "AMOC mentioned? False\n", "--------\n"]}], "source": ["for node in vector_response.source_nodes:\n", "    print(\"AMOC mentioned?\", \"AMOC\" in node.node.text)\n", "    print(\"--------\")"]}, {"cell_type": "code", "execution_count": 14, "id": "fd1207ae-bb99-464b-a633-c1edda5b2e20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Heightened risk of non-indigenous species immigration \n", "from vessel traffic plus climate change further endangers MPA success \n", "(<PERSON><PERSON><PERSON> et  al., 2020), a particular concern in the Mediterranean \n", "(<PERSON><PERSON>Amen and Azzurro, 2020; Mannino and Balistreri, 2021), where \n", "the current MPA network is already highly vulnerable to climate \n", "change (<PERSON><PERSON><PERSON><PERSON><PERSON> et  al., 2021). This new evidence supports SROCC’s \n", "high confidence assessment that present governance arrangements, \n", "including MPAs, are too fragmented to provide integrated responses \n", "to the increasing and cascading risks from climate change in the ocean \n", "(SROCC SPMC1.2; IPCC, 2019c).\n", "Strategic conservation planning can yield future MPA networks \n", "substantially more ready for climate change (e.g., Section  *******.5; \n", "SROCC SPM C2.1; IPCC, 2019c; Fraz<PERSON> et al., 2020; Rassweiler \n", "et  al., 2020). Global protection is increasing (<PERSON><PERSON>, 2017; <PERSON><PERSON> \n", "et  al., 2020b) as nations pursue international targets (e.g., SDG14, \n", "Life Below Water aimed to conserve 10% of the ocean by 2020), \n", "and the UN CBD proposes to protect 30% by 2030 (Section  3.6.4; \n", "SM3.5.3; CBD, 2020). A growing body of evidence (<PERSON><PERSON><PERSON><PERSON> et  al., \n", "2019; <PERSON> et  al., 2020a; <PERSON><PERSON><PERSON><PERSON> et  al., 2021b; <PERSON><PERSON> et  al., 2021) \n", "underscores the urgent need to pursue biodiversity, ecosystem-\n", "service provision and climate-adaptation goals simultaneously, while \n", "acknowledging inherent trade-offs (<PERSON><PERSON> et al., 2020a; <PERSON><PERSON> et al., \n", "2021). Frameworks to create ‘climate-smart’ MPAs (<PERSON><PERSON><PERSON><PERSON> et  al., \n", "2019) generally include: (a) defining conservation goals that embrace \n", "resource vulnerabilities and co-occurring hazards; (b)  carefully \n", "selecting adaptation strategies that include IKLK while respecting \n", "Indigenous rights and accommodating human behaviour (<PERSON><PERSON><PERSON><PERSON> et al., \n", "2017; <PERSON>, 2018; <PERSON> et  al., 2019; <PERSON><PERSON><PERSON> et  al., 2020; <PERSON> \n", "et al., 2020a; <PERSON><PERSON>, 2021; <PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON>, 2021); \n", "(c)  developing protection that is appropriate for all ocean depths \n", "(<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., \n", "2020a), especially considering climate velocity (<PERSON><PERSON><PERSON><PERSON><PERSON> et al., \n", "2021); (d) using dynamic national and international management tools \n", "to accommodate extreme events or species distribution shifts (Gaines \n", "et al., 2018; <PERSON><PERSON> et al., 2018; <PERSON><PERSON><PERSON> et al., 2019a; <PERSON><PERSON><PERSON><PERSON> and \n", "<PERSON><PERSON>l, 2019; <PERSON><PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., \n", "2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020b), which could \n", "build on dynamic regulations already in place for fishing or ship strikes \n", "(<PERSON> et  al., 2020b); and (e)  seeking to increase connectivity \n", "(<PERSON> et al., 2020a), using genomic or multi-species model insights \n", "(<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2021; Lima et al., 2021).\n", "There is growing international support for a 30% conservation target \n", "for 2030 (<PERSON><PERSON> et al., 2021), which will need efforts beyond protected \n", "areas. For example, OECMs recognise management interventions that \n", "sustain biodiversity, irrespective of their main objective (<PERSON> et al., \n", "2020b; <PERSON><PERSON> et al., 2021).\n"]}], "source": ["print(vector_response.source_nodes[2].node.text)"]}, {"cell_type": "code", "execution_count": 15, "id": "eeb66b14-d267-44be-b256-65f864562bd3", "metadata": {}, "outputs": [], "source": ["from llama_index.core.evaluation import DatasetGenerator, QueryResponseDataset\n", "\n", "from llama_index.llms.openai import OpenAI\n", "import nest_asyncio\n", "import random\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 16, "id": "2f93036b-b81e-49b8-8906-b07695f8a77b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2879732/2078797817.py:6: DeprecationWarning: Call to deprecated class DatasetGenerator. (Deprecated in favor of `RagDatasetGenerator` which should be used instead.)\n", "  dataset_generator = DatasetGenerator(\n"]}], "source": ["num_nodes_eval = 30\n", "# there are 428 nodes total. Take the first 200 to generate questions (the back half of the doc is all references)\n", "sample_eval_nodes = random.sample(base_nodes[:200], num_nodes_eval)\n", "# NOTE: run this if the dataset isn't already saved\n", "# generate questions from the largest chunks (1024)\n", "dataset_generator = DatasetGenerator(\n", "    sample_eval_nodes,\n", "    llm=OpenAI(model=\"gpt-4\"),\n", "    show_progress=True,\n", "    num_questions_per_chunk=2,\n", ")"]}, {"cell_type": "code", "execution_count": 17, "id": "b8e0d506-1fbe-475c-8c72-3bc33601a546", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████| 30/30 [00:12<00:00,  2.42it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:04<00:00,  2.24s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.43s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.52s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.74s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:04<00:00,  2.13s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.28s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.92s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.55s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.35s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.27s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:04<00:00,  2.20s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.82s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.89s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:04<00:00,  2.38s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.92s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:06<00:00,  3.30s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.35s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.57s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:13<00:00,  6.53s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.15s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:03<00:00,  1.95s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.15s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.96s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.73s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.99s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:03<00:00,  1.86s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:12<00:00,  6.21s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.58s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:12<00:00,  6.06s/it]\n", "100%|███████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.64s/it]\n", "/mnt/external_disk/venv/20250202_LlamaIndex/lib/python3.10/site-packages/llama_index/core/evaluation/dataset_generation.py:296: DeprecationWarning: Call to deprecated class QueryResponseDataset. (Deprecated in favor of `LabelledRagDataset` which should be used instead.)\n", "  return QueryResponseDataset(queries=queries, responses=responses_dict)\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'data/ipcc_eval_qr_dataset.json'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m eval_dataset \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m dataset_generator\u001b[38;5;241m.\u001b[39magenerate_dataset_from_nodes()\n\u001b[0;32m----> 2\u001b[0m \u001b[43meval_dataset\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43ms<PERSON>_json\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mdata/ipcc_eval_qr_dataset.json\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# optional\u001b[39;00m\n\u001b[1;32m      4\u001b[0m eval_dataset \u001b[38;5;241m=\u001b[39m QueryResponseDataset\u001b[38;5;241m.\u001b[39mfrom_json(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata/ipcc_eval_qr_dataset.json\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/mnt/external_disk/venv/20250202_LlamaIndex/lib/python3.10/site-packages/llama_index/core/evaluation/dataset_generation.py:101\u001b[0m, in \u001b[0;36mQueryResponseDataset.save_json\u001b[0;34m(self, path)\u001b[0m\n\u001b[1;32m     99\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msave_json\u001b[39m(\u001b[38;5;28mself\u001b[39m, path: \u001b[38;5;28mstr\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    100\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Save json.\"\"\"\u001b[39;00m\n\u001b[0;32m--> 101\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mw\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m    102\u001b[0m         json\u001b[38;5;241m.\u001b[39mdump(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_dump(), f, indent\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m4\u001b[39m)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'data/ipcc_eval_qr_dataset.json'"]}], "source": ["# 创建一个Evaluation数据集\n", "# eval_dataset = await dataset_generator.agenerate_dataset_from_nodes()\n", "# eval_dataset.save_json(\"90-文档-Data/复杂PDF/ipcc_eval_qr_dataset.json\")\n", "\n", "eval_dataset = QueryResponseDataset.from_json(\"90-文档-Data/复杂PDF/ipcc_eval_qr_dataset.json\")"]}, {"cell_type": "code", "execution_count": 19, "id": "ab249c47-eb09-4f5c-bfa3-9b7103448aa1", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 20, "id": "07805a9e-439c-4ed5-b1e4-9a9ae37db975", "metadata": {}, "outputs": [], "source": ["from llama_index.core.evaluation import (\n", "    CorrectnessEvaluator,\n", "    SemanticSimilarityEvaluator,\n", "    RelevancyEvaluator,\n", "    FaithfulnessEvaluator,\n", "    PairwiseComparisonEvaluator,\n", ")\n", "\n", "\n", "from collections import defaultdict\n", "import pandas as pd\n", "\n", "# NOTE: can uncomment other evaluators\n", "evaluator_c = CorrectnessEvaluator(llm=OpenAI(model=\"gpt-4\"))\n", "evaluator_s = SemanticSimilarityEvaluator()\n", "evaluator_r = RelevancyEvaluator(llm=OpenAI(model=\"gpt-4\"))\n", "evaluator_f = FaithfulnessEvaluator(llm=OpenAI(model=\"gpt-4\"))\n", "# pairwise_evaluator = PairwiseComparisonEvaluator(llm=OpenAI(model=\"gpt-4\"))"]}, {"cell_type": "code", "execution_count": 21, "id": "ab2c04be-7ff4-4680-a503-915084a5a76d", "metadata": {}, "outputs": [], "source": ["from llama_index.core.evaluation.eval_utils import (\n", "    get_responses,\n", "    get_results_df,\n", ")\n", "from llama_index.core.evaluation import BatchEvalRunner\n", "\n", "max_samples = 30\n", "\n", "eval_qs = eval_dataset.questions\n", "ref_response_strs = [r for (_, r) in eval_dataset.qr_pairs]\n", "\n", "# resetup base query engine and sentence window query engine\n", "# base query engine\n", "base_query_engine = base_index.as_query_engine(similarity_top_k=2)\n", "# sentence window query engine\n", "query_engine = sentence_index.as_query_engine(\n", "    similarity_top_k=2,\n", "    # the target key defaults to `window` to match the node_parser's default\n", "    node_postprocessors=[\n", "        MetadataReplacementPostProcessor(target_metadata_key=\"window\")\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": 22, "id": "ef5dbb96-c925-4319-af96-119919ac1e3a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████████| 30/30 [00:03<00:00,  8.40it/s]\n", "100%|█████████████████████████████████████████████████████████████████████████████| 30/30 [00:15<00:00,  1.90it/s]\n"]}], "source": ["import numpy as np\n", "\n", "base_pred_responses = get_responses(\n", "    eval_qs[:max_samples], base_query_engine, show_progress=True\n", ")\n", "pred_responses = get_responses(\n", "    eval_qs[:max_samples], query_engine, show_progress=True\n", ")\n", "\n", "pred_response_strs = [str(p) for p in pred_responses]\n", "base_pred_response_strs = [str(p) for p in base_pred_responses]"]}, {"cell_type": "code", "execution_count": 23, "id": "491e071d-a5be-4662-b7c0-e45acf6be565", "metadata": {}, "outputs": [], "source": ["evaluator_dict = {\n", "    \"correctness\": evaluator_c,\n", "    \"faithfulness\": evaluator_f,\n", "    \"relevancy\": evaluator_r,\n", "    \"semantic_similarity\": evaluator_s,\n", "}\n", "batch_runner = <PERSON><PERSON><PERSON><PERSON>Runner(evaluator_dict, workers=2, show_progress=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "5277d1a7-446b-4704-bf3e-da5846b857f0", "metadata": {}, "outputs": [], "source": ["evaluator_dict = {\n", "    \"correctness\": evaluator_c,\n", "    \"faithfulness\": evaluator_f,\n", "    \"relevancy\": evaluator_r,\n", "    \"semantic_similarity\": evaluator_s,\n", "}\n", "batch_runner = <PERSON><PERSON><PERSON><PERSON>Runner(evaluator_dict, workers=2, show_progress=True)"]}, {"cell_type": "code", "execution_count": 25, "id": "b7ab53d0-a221-4051-99b3-58f043de560d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████| 120/120 [01:24<00:00,  1.42it/s]\n", "100%|███████████████████████████████████████████████████████████████████████████| 120/120 [01:26<00:00,  1.39it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>names</th>\n", "      <th>correctness</th>\n", "      <th>relevancy</th>\n", "      <th>faithfulness</th>\n", "      <th>semantic_similarity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Sentence Window Retriever</td>\n", "      <td>4.500000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.933759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Base Retriever</td>\n", "      <td>4.366667</td>\n", "      <td>0.966667</td>\n", "      <td>0.966667</td>\n", "      <td>0.909008</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       names  correctness  relevancy  faithfulness  \\\n", "0  Sentence Window Retriever     4.500000   1.000000      1.000000   \n", "1             Base Retriever     4.366667   0.966667      0.966667   \n", "\n", "   semantic_similarity  \n", "0             0.933759  \n", "1             0.909008  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["eval_results = await batch_runner.aevaluate_responses(\n", "    queries=eval_qs[:max_samples],\n", "    responses=pred_responses[:max_samples],\n", "    reference=ref_response_strs[:max_samples],\n", ")\n", "base_eval_results = await batch_runner.aevaluate_responses(\n", "    queries=eval_qs[:max_samples],\n", "    responses=base_pred_responses[:max_samples],\n", "    reference=ref_response_strs[:max_samples],\n", ")\n", "results_df = get_results_df(\n", "    [eval_results, base_eval_results],\n", "    [\"Sentence Window Retriever\", \"Base Retriever\"],\n", "    [\"correctness\", \"relevancy\", \"faithfulness\", \"semantic_similarity\"],\n", ")\n", "display(results_df)"]}, {"cell_type": "code", "execution_count": null, "id": "ca2448de-c990-4b87-83f0-b1db08e36993", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "db594b34-2a01-4d34-a64f-74c64606c21b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dd2216c8-0147-4641-8c41-f04d6ba7c91d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ab31fa24-2654-4900-8dba-fb5f93c2510a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5c7a0be1-bb81-4edf-9bf6-b64e64766ab4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (20250202_LlamaIndex)", "language": "python", "name": "20250202_llamaindex"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}