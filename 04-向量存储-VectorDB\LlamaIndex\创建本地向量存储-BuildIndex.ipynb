{"cells": [{"cell_type": "markdown", "id": "6a342c03-ea81-4956-b0d1-ca5c37b11d46", "metadata": {}, "source": ["**Index**是LlamaIndex中的基础数据结构，用于组织和存储文档或数据。  \n", "主要特点：  \n", "\n", "- 将原始数据转换为可检索的结构  \n", "- 支持多种索引类型，如向量存储、树状结构等  \n", "- 便于快速检索相关信息  \n", "\n", "常见类型：  \n", "\n", "- VectorStoreIndex：使用向量表示存储文档，适合语义搜索  \n", "- ListIndex：简单的列表结构，适合小型数据集  \n", "- TreeIndex：树状结构，适合层次化数据  \n", "\n", "用途：  \n", "\n", "- 高效存储和组织大量文档  \n", "- 支持复杂的查询和检索操作  "]}, {"cell_type": "code", "execution_count": 2, "id": "ae001717-011d-4333-afba-38fd27bc13c7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["D:\\venv\\llamaindex_venv\\Lib\\site-packages\\pypdf\\_crypt_providers\\_cryptography.py:32: CryptographyDeprecationWarning: ARC4 has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.ARC4 and will be removed from this module in 48.0.0.\n", "  from cryptography.hazmat.primitives.ciphers.algorithms import AES, ARC4\n"]}], "source": ["from llama_index.core import SimpleDirectoryReader\n", "\n", "documents = SimpleDirectoryReader(\"90-文档-Data\").load_data()"]}, {"cell_type": "code", "execution_count": 3, "id": "3d7b8713-4e86-47bd-9d85-768518347c8a", "metadata": {}, "outputs": [{"data": {"text/plain": ["11"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(documents)"]}, {"cell_type": "code", "execution_count": 2, "id": "6038989a-d97c-41f0-a84d-a3c705426887", "metadata": {}, "outputs": [], "source": ["from llama_index.core import VectorStoreIndex\n", "index = VectorStoreIndex.from_documents(documents)"]}, {"cell_type": "code", "execution_count": null, "id": "a8dda58d-82d6-4ce0-b0b2-e9f5f8de6a77", "metadata": {}, "outputs": [], "source": ["vars(index)"]}, {"cell_type": "code", "execution_count": 4, "id": "3c43dd1c-2755-4981-bfd9-3bc153ffde93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4a2aedf6-6c9e-4359-9125-786119be2b50\n", "4db844a0-5e4e-4fb9-a040-1ab7e3854d26\n", "b44113e0-2dc5-47d3-9666-1d0d70c89620\n", "e83766f0-1762-4c43-82b9-3ba71fabb81d\n", "15be4126-b8c0-483b-8bdf-d9a61a3b80e6\n", "77f2ab3f-4ac0-433d-9b5a-f3fa442587a5\n", "07388425-9662-4690-b0b4-6ede6943561d\n"]}], "source": ["nodes = index.index_struct.nodes_dict\n", "for node in nodes:\n", "    print(node)"]}, {"cell_type": "markdown", "id": "22ce158b-3e1b-4b38-bde8-6b515daed488", "metadata": {}, "source": ["LlamaIndex自动的切分Nodes"]}, {"cell_type": "code", "execution_count": 6, "id": "e4230ed3-5b4d-4a99-877b-c3d83c4b006d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6e95bafe-5646-4ae5-90b2-3174a4ae2792\n", "733101e5-c3e8-4f93-bf1f-c5505ebd4ec7\n", "d5759151-2acd-4d04-8dee-dbcd2e6aa70b\n", "74bd2040-3640-477b-8224-9e36d7b99272\n", "0702c10c-36e4-470b-bc7e-515c0cc913a3\n", "8ec705ac-40fa-4b06-b842-b3c7bb03b0b6\n", "e1a5c4ff-1226-4ec7-b7c7-ef1862613336\n", "f7df0aa0-334d-4acc-a06b-ca53fe99dc9e\n", "cc540951-e830-4170-8556-80fe524d8ae2\n", "75aaed7d-ef02-45db-acee-10be447bb34d\n", "0202eb3e-85d0-40c3-9efe-0df4f1a0557b\n"]}], "source": ["from llama_index.core.node_parser import SentenceSplitter\n", "\n", "text_splitter = SentenceSplitter(chunk_size=512, chunk_overlap=10)\n", "nodes = text_splitter.get_nodes_from_documents(documents)\n", "index = VectorStoreIndex(nodes) # 从nodes中生成Index\n", "nodes = index.index_struct.nodes_dict\n", "for node in nodes:\n", "    print(node)"]}, {"cell_type": "code", "execution_count": 7, "id": "f2ff0c3d-d2fa-41a1-bf4d-87947ab8d60e", "metadata": {}, "outputs": [{"data": {"text/plain": ["11"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(nodes)"]}, {"cell_type": "markdown", "id": "0b4e11d4-e9ec-41e6-a48e-7294aa895009", "metadata": {}, "source": ["节点和节点之间的关联，以及节点和Embedding之间的关联都被详细的记录了。\n", "{\"docstore/metadata\": {\"2ba7f0a4-6653-4d0f-8cd0-ab8f6b64966b\": {\"doc_hash\": \"6daae38b6369ae0c64bafcf2843f2141827f4a66fa1c5e82a6803ce3e17eb8ff\"}, \"e1920765-8109-43f1-aa7e-7983af103bac\": {\"doc_hash\": \"acecdf06e0e46aba6256a5d7b4d1889bdef89f8147c6888f94a64d0d7930e093\"}, \"bd8a7b3b-47f0-438a-97fa-7cb3eb60bb52\": {\"doc_hash\": \"6b576d9e03f86e6ec8ae094f57b2b45bb95e341ab5d5c53540620b72077cad54\"}, \"29eae8f9-5011-4ec2-93aa-0f9180e90386\": {\"doc_hash\": \"6daae38b6369ae0c64bafcf2843f2141827f4a66fa1c5e82a6803ce3e17eb8ff\", \"ref_doc_id\": \"2ba7f0a4-6653-4d0f-8cd0-ab8f6b64966b\"}, \"ad41d627-9bff-4386-95e3-63e5a2668168\": {\"doc_hash\": \"1bf90e642e0a2d79727a829f244e03efd472efae12b21214c621b99f49e45d8c\", \"ref_doc_id\": \"e1920765-8109-43f1-aa7e-7983af103bac\"}, \"e8b32c12-51ab-42da-ae2f-1def0f42f66c\": {\"doc_hash\": \"02ee285d684e527f75d03e09a8a84b0bb2a2f8dc1dabbcfe8c0779cfa3fcd786\", \"ref_doc_id\": \"e1920765-8109-43f1-aa7e-7983af103bac\"}, \"a4125b50-2209-48dc-b2b3-94d43cade044\": {\"doc_hash\": \"1a67d9f45b23ed9115745cb1ba2a3be602b66b179c5ad000d0029dd2540f3bf1\", \"ref_doc_id\": \"e1920765-8109-43f1-aa7e-7983af103bac\"}, \"e5b0e38e-bb4f-484d-9018-8b622f9bec5a\": {\"doc_hash\": \"3530185768a7ccf33c85a6feab63fca1a1791e203e33e425af0f076a8c19194e\", \"ref_doc_id\": \"e1920765-8109-43f1-aa7e-7983af103bac\"}, \"711f6edf-d561-4862-90f9-d508b7c2ab90\": {\"doc_hash\": \"bf6c7726b6caf1dddbcfc3d3f298c7d2a4496aaf2c867b17ac06a6b2b57f2979\", \"ref_doc_id\": \"e1920765-8109-43f1-aa7e-7983af103bac\"}, \"911beded-3ab0-4e85-87d9-6acf68c51495\": {\"doc_hash\": \"6b576d9e03f86e6ec8ae094f57b2b45bb95e341ab5d5c53540620b72077cad54\", \"ref_doc_id\": \"bd8a7b3b-47f0-438a-97fa-7cb3eb60bb52\"}}, \"docstore/data\": {\"29eae8f9-5011-4ec2-93aa-0f9180e90386\": {\"__data__\": {\"id_\": \"29eae8f9-5011-4ec2-93aa-0f9180e90386\", \"embedding\": null, \"metadata\": {\"page_label\": \"1\", \"file_name\": \"TestOCR.pdf\", \"file_path\": \"/home/<USER>/Documents/08_RAG/LlamaIndex/00_Data/TestOCR.pdf\", \"file_type\": \"application/pdf\", \"file_size\": 26662, \"creation_date\": \"2024-08-10\", \"last_modified_date\": \"2024-08-10\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"2ba7f0a4-6653-4d0f-8cd0-ab8f6b64966b\", \"node_type\": \"4\", \"metadata\": {\"page_label\": \"1\", \"file_name\": \"TestOCR.pdf\", \"file_path\": \"/home/<USER>/Documents/08_RAG/LlamaIndex/00_Data/TestOCR.pdf\", \"file_type\": \"application/pdf\", \"file_size\": 26662, \"creation_date\": \"2024-08-10\", \"last_modified_date\": \"2024-08-10\"}, \"hash\": \"6daae38b6369ae0c64bafcf2843f2141827f4a66fa1c5e82a6803ce3e17eb8ff\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"This isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\\nThis isasample document totestthePDF Image+Text OCR Engine.\", \"mimetype\": \"text/plain\", \"start_char_idx\": 0, \"end_char_idx\": 959, \"text_template\": \"{metadata_str}\\n\\n{content}\", \"metadata_template\": \"{key}: {value}\", \"metadata_seperator\": \"\\n\", \"class_name\": \"TextNode\"}, \"__type__\": \"1\"}, \"ad41d627-9bff-4386-95e3-63e5a2668168\": {\"__data__\": {\"id_\": \"ad41d627-9bff-4386-95e3-63e5a2668168\", \"embedding\": null, \"metadata\": {\"file_path\": \"/home/<USER>/Documents/08_RAG/LlamaIndex/00_Data/sample.txt\", \"file_name\": \"sample.txt\", \"file_type\": \"text/plain\", \"file_size\": 9770, \"creation_date\": \"2024-08-15\", \"last_modified_date\": \"2024-08-15\"}, \"excluded_embed_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"excluded_llm_metadata_keys\": [\"file_name\", \"file_type\", \"file_size\", \"creation_date\", \"last_modified_date\", \"last_accessed_date\"], \"relationships\": {\"1\": {\"node_id\": \"e1920765-8109-43f1-aa7e-7983af103bac\", \"node_type\": \"4\", \"metadata\": {\"file_path\": \"/home/<USER>/Documents/08_RAG/LlamaIndex/00_Data/sample.txt\", \"file_name\": \"sample.txt\", \"file_type\": \"text/plain\", \"file_size\": 9770, \"creation_date\": \"2024-08-15\", \"last_modified_date\": \"2024-08-15\"}, \"hash\": \"acecdf06e0e46aba6256a5d7b4d1889bdef89f8147c6888f94a64d0d7930e093\", \"class_name\": \"RelatedNodeInfo\"}, \"3\": {\"node_id\": \"e8b32c12-51ab-42da-ae2f-1def0f42f66c\", \"node_type\": \"1\", \"metadata\": {}, \"hash\": \"13d72b2ca343f5bcecbfd6886258b4519fdda2179f9403a44530fa40f36baabe\", \"class_name\": \"RelatedNodeInfo\"}}, \"text\": \"\\u5bcc\\u58eb\\u5eb7\\u201c\\u6389\\u5934\\u201d\\uff0c\\u5bf9\\u6cb3\\u5357\\u610f\\u5473\\u7740\\u4ec0\\u4e48\\r\\n\\r\\n(\\u88ab\\u9605\\u8bfb 9977 \\u6b21)A- A A+\\r\\n\\r\\n\\u5982\\u4f55\\u7eed\\u5199\\u65b0\\u6545\\u4e8b\\uff1f\\r\\n\\r\\n\\u5bcc\\u58eb\\u5eb7\\u201c\\u51fa\\u8d70\\u201d\\u7684\\u58f0\\u97f3\\u6b64\\u524d\\u65f6\\u6709\\u51fa\\u73b0\\uff0c\\u800c\\u73b0\\u5728\\uff0c\\u4e00\\u7cfb\\u5217\\u4fe1\\u53f7\\u663e\\u793a\\uff0c\\u5bcc\\u58eb\\u5eb7\\u53c8\\u201c\\u56de\\u6765\\u201d\\u4e86\\u3002\\r\\n\\r\\n\\r\\n\\u8fd1\\u65e5\\uff0c\\u90d1\\u5dde\\u5bcc\\u58eb\\u5eb7\\u62db\\u5de5\\u7684\\u6d88\\u606f\\u53d7\\u5230\\u5e7f\\u6cdb\\u5173\\u6ce8\\u3002\\u636e\\u5a92\\u4f53\\u62a5\\u9053\\uff0c\\u4e34\\u8fd1\\u82f9\\u679c\\u65b0\\u673a\\u578b\\u53d1\\u5e03\\uff0c\\u5927\\u6279\\u52b3\\u52a8\\u529b\\u6d8c\\u5411\\u90d1\\u5dde\\u5bcc\\u58eb\\u5eb7\\uff0c\\u4e2d\\u4ecb\\u79f0\\u8fd1\\u4e24\\u5468\\u8fdb\\u5382\\u65b0\\u5458\\u5de5\\u6700\\u5c11\\u67095\\u4e07\\u4eba\\uff0c\\u5e76\\u4e14\\u8fd8\\u5728\\u5927\\u89c4\\u6a21\\u62db\\u8058\\u3002\\r\\n\\r\\n7\\u670824\\u65e5\\uff0c\\u5bcc\\u58eb\\u5eb7\\u6bcd\\u516c\\u53f8\\u9e3f\\u6d77\\u53d1\\u5e03\\u516c\\u544a\\uff0c\\u5bcc\\u58eb\\u5eb7\\u79d1\\u6280\\u96c6\\u56e2\\u5c06\\u5728\\u6cb3\\u5357\\u90d1\\u5dde\\u6295\\u8d4410\\u4ebf\\u5143\\uff0c\\u5efa\\u8bbe\\u65b0\\u4e8b\\u4e1a\\u603b\\u90e8\\u5927\\u697c\\u3002\\u8be5\\u9879\\u76ee\\u6d89\\u53ca\\u603b\\u90e8\\u7ba1\\u7406\\u3001\\u7814\\u53d1\\u3001\\u6218\\u7565\\u4ea7\\u4e1a\\u53d1\\u5c55\\u3001\\u4f9b\\u5e94\\u94fe\\u7ba1\\u7406\\u7b49\\u4e03\\u5927\\u4e2d\\u5fc3\\uff0c\\u8fd8\\u5c06\\u5e03\\u5c40\\u7535\\u52a8\\u8f66\\u5236\\u9020\\u548c\\u56fa\\u6001\\u7535\\u6c60\\u9879\\u76ee\\u3002\\r\\n\\r\\n\\u4f5c\\u4e3a\\u5168\\u7403\\u6700\\u5927\\u7684\\u7535\\u5b50\\u4ee3\\u5de5\\u5382\\uff0c\\u5bcc\\u58eb\\u5eb7\\u5728\\u4e2d\\u56fd\\u8bbe\\u6709\\u6570\\u91cf\\u6700\\u591a\\u7684\\u5de5\\u5382\\uff0c\\u5176\\u4e2d\\u90d1\\u5dde\\u5bcc\\u58eb\\u5eb7\\u56ed\\u533a\\u7684\\u5730\\u4f4d\\u4e3e\\u8db3\\u8f7b\\u91cd\\u3002\\u5bf9\\u4e8e\\u6cb3\\u5357\\u800c\\u8a00\\uff0c\\u5bcc\\u58eb\\u5eb7\\u5219\\u8d21\\u732e\\u4e86\\u5927\\u91cf\\u7684\\u5c31\\u4e1a\\u5c97\\u4f4d\\u4ee5\\u53ca\\u51fa\\u53e3\\u8d27\\u7269\\u91cf\\uff0c\\u52a9\\u529b\\u5730\\u65b9\\u7ecf\\u6d4e\\u53d1\\u5c55\\u3002\\r\\n\\r\\n\\u6cb3\\u5357\\u548c\\u5bcc\\u58eb\\u5eb7\\uff0c\\u4f3c\\u4e4e\\u96be\\u4ee5\\u79bb\\u5f00\\u5f7c\\u6b64\\u3002\\u800c\\u5f53\\u4e0b\\u7684\\u6545\\u4e8b\\uff0c\\u53c8\\u4e0e\\u4e4b\\u524d\\u6709\\u6240\\u4e0d\\u540c\\u3002\\r\\n\\r\\n\\u7275\\u624b\\u72c2\\u5954\\r\\n\\r\\n2010\\u5e74\\uff0c\\u5bcc\\u58eb\\u5eb7\\u5165\\u9a7b\\u90d1\\u5dde\\u822a\\u7a7a\\u6e2f\\u533a\\uff0c\\u6b63\\u5f0f\\u5f00\\u59cb\\u5728\\u90d1\\u5dde\\u5efa\\u5382\\u3002\\u4e4b\\u540e\\u7684\\u4e00\\u6bb5\\u65f6\\u95f4\\uff0c\\u6b63\\u662f\\u667a\\u80fd\\u624b\\u673a\\u5728\\u4e2d\\u56fd\\u5feb\\u901f\\u53d1\\u5c55\\u7684\\u5e74\\u4ee3\\uff0c\\u4e3b\\u8981\\u751f\\u4ea7\\u82f9\\u679c\\u624b\\u673a\\u7684\\u90d1\\u5dde\\u5bcc\\u58eb\\u5eb7\\u4e0d\\u65ad\\u6269\\u5927\\u89c4\\u6a21\\uff0c\\u751a\\u81f3\\u6210\\u4e3a\\u5bcc\\u58eb\\u5eb7\\u5728\\u5168\\u7403\\u6700\\u5927\\u7684\\u5382\\u533a\\u3002\\r\\n\\r\\n\\r\\n\\u5bcc\\u58eb\\u5eb7\\u90d1\\u5dde\\u79d1\\u6280\\u56ed\\u533a \\u56fe\\u7247\\u6765\\u6e90\\uff1a\\u6bcf\\u65e5\\u7ecf\\u6d4e\\u65b0\\u95fb\\uff08\\u738b\\u4f73\\u98de "]}, {"cell_type": "code", "execution_count": 8, "id": "b75e1b92-1ea3-473b-8c3f-825896793a39", "metadata": {}, "outputs": [], "source": ["# 保存索引到磁盘\n", "index.storage_context.persist(persist_dir=\"saved_index\")"]}, {"cell_type": "code", "execution_count": null, "id": "0216c17d-af25-44c9-91ff-d83007e91868", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (20250202_LlamaIndex)", "language": "python", "name": "20250202_llamaindex"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}