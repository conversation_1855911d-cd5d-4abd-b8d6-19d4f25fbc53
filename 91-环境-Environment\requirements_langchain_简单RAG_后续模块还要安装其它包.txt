aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
annotated-types==0.7.0
anthropic==0.49.0
anyio==4.9.0
asttokens==3.0.0
attrs==25.3.0
beautifulsoup4==4.13.3
certifi==2025.1.31
charset-normalizer==3.4.1
colorama==0.4.6
comm==0.2.2
dataclasses-json==0.6.7
debugpy==1.8.13
decorator==5.2.1
distro==1.9.0
executing==2.2.0
faiss-cpu==1.10.0
filelock==3.18.0
frozenlist==1.5.0
fsspec==2025.3.0
greenlet==3.1.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.3
idna==3.10
ipykernel==6.29.5
ipython
ipython_pygments_lexers==1.1.1
jedi==0.19.2
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jupyter_client==8.6.3
jupyter_core==5.7.2
langchain==0.3.21
langchain-community==0.3.20
langchain-core==0.3.47
langchain-deepseek==0.1.2
langchain-huggingface==0.1.2
langchain-openai==0.3.9
langchain-text-splitters==0.3.7
langgraph==0.3.18
langgraph-checkpoint==2.0.21
langgraph-prebuilt==0.1.3
langgraph-sdk==0.1.58
langsmith==0.3.18
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib-inline==0.1.7
mpmath==1.3.0
msgpack==1.1.0
multidict==6.2.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
numpy==2.2.4
openai==1.68.2
orjson==3.10.15
packaging==24.2
parso==0.8.4
pillow==11.1.0
platformdirs==4.3.7
prompt_toolkit==3.0.50
propcache==0.3.0
psutil==7.0.0
pure_eval==0.2.3
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
Pygments==2.19.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
PyYAML==6.0.2
pyzmq==26.3.0
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==3.4.1
setuptools==77.0.3
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.39
stack-data==0.6.3
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
torch==2.6.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.50.0
typing-inspect==0.9.0
typing_extensions==4.12.2
urllib3==2.3.0
wcwidth==0.2.13
yarl==1.18.3
zstandard==0.23.0
