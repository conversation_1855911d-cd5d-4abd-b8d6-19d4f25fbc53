# 1. 加载文档
import os
from dotenv import load_dotenv
# 加载环境变量
load_dotenv()

# --- 配置ECOVAI API ---
ECOVAI_API_KEY = "sk-w20CB5cuk4bZgTLmgMyWczkGSsL1iwLDieapWVfwYsRufklO"
ECOVAI_BASE_URL = "https://api.ecovai.cn/v1"
ECOVAI_LLM_MODEL = "DeepSeek-V3-0324"
ECOVAI_EMBEDDING_MODEL = "text-embedding-3-small"

from langchain_community.document_loaders import WebBaseLoader

loader = WebBaseLoader(
    web_paths=("https://zh.wikipedia.org/wiki/黑神话：悟空",)
)
docs = loader.load()

# 2. 文档分块
from langchain_text_splitters import RecursiveCharacterTextSplitter

text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
all_splits = text_splitter.split_documents(docs)

# 3. 设置嵌入模型 - 使用ECOVAI的OpenAI兼容嵌入模型
from langchain_openai import OpenAIEmbeddings

embeddings = OpenAIEmbeddings(
    model=ECOVAI_EMBEDDING_MODEL,
    api_key=ECOVAI_API_KEY,
    base_url=ECOVAI_BASE_URL
)

# 4. 创建向量存储
from langchain_core.vectorstores import InMemoryVectorStore

vector_store = InMemoryVectorStore(embeddings)
vector_store.add_documents(all_splits)

# 5. 构建用户查询
question = "黑悟空有哪些游戏场景？"

# 6. 在向量存储中搜索相关文档，并准备上下文内容
retrieved_docs = vector_store.similarity_search(question, k=3)
docs_content = "\n\n".join(doc.page_content for doc in retrieved_docs)

# 7. 构建提示模板
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template("""
                基于以下上下文，回答问题。如果上下文中没有相关信息，
                请说"我无法从提供的上下文中找到相关信息"。
                上下文: {context}
                问题: {question}
                回答:"""
                                          )

# 8. 使用大语言模型生成答案 - 使用ECOVAI的DeepSeek模型
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(
    model=ECOVAI_LLM_MODEL,  # ECOVAI API 支持的DeepSeek模型
    base_url=ECOVAI_BASE_URL,
    temperature=0.7,        # 控制输出的随机性(0-1之间,越大越随机)
    max_tokens=2048,        # 最大输出长度
    top_p=0.95,            # 控制输出的多样性(0-1之间)
    presence_penalty=0.0,   # 重复惩罚系数(-2.0到2.0之间)
    frequency_penalty=0.0,  # 频率惩罚系数(-2.0到2.0之间)
    api_key=ECOVAI_API_KEY  # 使用ECOVAI API key
)
answer = llm.invoke(prompt.format(question=question, context=docs_content))
print(answer)


