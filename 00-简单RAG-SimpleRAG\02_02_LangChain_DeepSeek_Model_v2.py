# 1. 加载文档
import os
from dotenv import load_dotenv
# 加载环境变量
load_dotenv()

# --- 配置ECOVAI API ---
ECOVAI_API_KEY = "sk-w20CB5cuk4bZgTLmgMyWczkGSsL1iwLDieapWVfwYsRufklO"
ECOVAI_BASE_URL = "https://api.ecovai.cn/v1"
ECOVAI_LLM_MODEL = "DeepSeek-V3-0324"
ECOVAI_EMBEDDING_MODEL = "text-embedding-3-small"

# 尝试从维基百科加载黑神话：悟空的信息，如果失败则使用本地内容
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.documents import Document

try:
    print("正在尝试从维基百科加载内容...")
    loader = WebBaseLoader(
        web_paths=("https://baike.baidu.com/item/%E9%BB%91%E7%A5%9E%E8%AF%9D/64841329",)
    )
    docs = loader.load()
    print(f"成功从维基百科加载了 {len(docs)} 个文档")
except Exception as e:
    print(f"从维基百科加载失败: {e}")
    print("使用本地示例内容...")
    # 使用从维基百科获取的真实内容作为备用
    docs = [Document(page_content="""
    《黑神话：悟空》是一款由游戏科学开发和发行的动作角色扮演游戏，被新华社称为中国首款"3A游戏"，游戏于2024年8月20日登入Windows（Steam、Epic Games、WeGame）及PlayStation 5平台，2025年8月20日登录Xbox Series X/S平台。

    游戏内容改编自中国古典神魔小說《西游记》，在正式发布前，游戏已获得业界媒体与评论家们的普遍好评，称赞其在战斗系统、视觉设计以及世界观方面的构建。

    游戏场景设定：
    游戏的设定融合了中國的文化和自然地标。例如重慶的大足石刻、山西省的小西天、南禅寺、铁佛寺、广胜寺和鹳雀楼等，都在游戏中出现。游戏也融入了佛教和道教的哲学元素。

    主要游戏场景包括：
    1. 花果山 - 这里是齐天大圣孙悟空的出生地。山上常年缭绕着仙气，瀑布从千米高空倾泻而下，形成"天河飞瀑"。山中生长着各种仙草灵药，还有不少修炼成精的动物。
    2. 水帘洞 - 位于花果山之巅，洞前有一道天然形成的水帘，既是天然屏障，也是修炼圣地。
    3. 黑风山 - 游戏第一章的主要场景，火后重建的观音禅院所在地
    4. 黄风岭 - 荒凉的地区，黄风大圣的领域
    5. 小西天 - 寒冷的区域，黄眉大王的据点
    6. 盘丝洞 - 蜘蛛精的巢穴，充满危险
    7. 火焰山 - 炽热的山脉，牛魔王的领域

    游戏特色：
    - 精美的中国风画面和传统文化元素
    - 丰富的战斗系统，包括棍法、法术和变化
    - 多样化的敌人和Boss战
    - 深度的角色成长系统
    - 融合佛教和道教哲学的世界观
    """, metadata={"source": "wikipedia_backup"})]

# 2. 文档分块
from langchain_text_splitters import RecursiveCharacterTextSplitter

text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
all_splits = text_splitter.split_documents(docs)

# 3. 设置嵌入模型 - 使用ECOVAI的OpenAI兼容嵌入模型
from langchain_openai import OpenAIEmbeddings

embeddings = OpenAIEmbeddings(
    model=ECOVAI_EMBEDDING_MODEL,
    api_key=ECOVAI_API_KEY,
    base_url=ECOVAI_BASE_URL
)

# 4. 创建向量存储
from langchain_core.vectorstores import InMemoryVectorStore

vector_store = InMemoryVectorStore(embeddings)
vector_store.add_documents(all_splits)

# 5. 构建用户查询
question = "黑悟空有哪些游戏场景？"

# 6. 在向量存储中搜索相关文档，并准备上下文内容
retrieved_docs = vector_store.similarity_search(question, k=3)
docs_content = "\n\n".join(doc.page_content for doc in retrieved_docs)

# 7. 构建提示模板
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template("""
                基于以下上下文，回答问题。如果上下文中没有相关信息，
                请说"我无法从提供的上下文中找到相关信息"。
                上下文: {context}
                问题: {question}
                回答:"""
                                          )

# 8. 使用大语言模型生成答案 - 使用ECOVAI的DeepSeek模型
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(
    model=ECOVAI_LLM_MODEL,  # ECOVAI API 支持的DeepSeek模型
    base_url=ECOVAI_BASE_URL,
    temperature=0.7,        # 控制输出的随机性(0-1之间,越大越随机)
    max_tokens=2048,        # 最大输出长度
    top_p=0.95,            # 控制输出的多样性(0-1之间)
    presence_penalty=0.0,   # 重复惩罚系数(-2.0到2.0之间)
    frequency_penalty=0.0,  # 频率惩罚系数(-2.0到2.0之间)
    api_key=ECOVAI_API_KEY  # 使用ECOVAI API key
)
answer = llm.invoke(prompt.format(question=question, context=docs_content))
print(answer)


