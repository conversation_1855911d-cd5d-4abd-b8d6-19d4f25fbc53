aiohappyeyeballs==2.6.1
aiohttp==3.11.14
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
beautifulsoup4==4.13.3
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
dataclasses-json==0.6.7
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
filelock==3.18.0
filetype==1.2.0
frozenlist==1.5.0
fsspec==2025.3.0
greenlet==3.1.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.3
idna==3.10
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
llama-cloud==0.1.15
llama-cloud-services==0.6.6
llama-index==0.12.25
llama-index-agent-openai==0.4.6
llama-index-cli==0.4.1
llama-index-core==0.12.25
llama-index-embeddings-huggingface==0.5.2
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.9
llama-index-llms-deepseek==0.1.1
llama-index-llms-openai==0.3.26
llama-index-llms-openai-like==0.3.4
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.6
llama-index-readers-llama-parse==0.4.0
llama-parse==0.6.4.post1
MarkupSafe==3.0.2
marshmallow==3.26.1
mpmath==1.3.0
multidict==6.2.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.4
openai==1.68.0
packaging==24.2
pandas==2.2.3
pillow==11.1.0
propcache==0.3.0
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pypdf==5.4.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.1
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==3.4.1
setuptools==77.0.3
six==1.17.0
sniffio==1.3.1
sounddevice==0.5.1
soupsieve==2.6
SQLAlchemy==2.0.39
striprtf==0.0.26
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
torch==2.6.0
tqdm==4.67.1
transformers==4.49.0
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
urllib3==2.3.0
wrapt==1.17.2
yarl==1.18.3
