"""
注意：此代码已配置使用ECOVAI转接的OpenAI兼容API。
语言模型：DeepSeek-V3-0324
嵌入模型：text-embedding-3-small
API地址：https://api.ecovai.cn/v1
"""

# 导入相关的库
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, Settings
from llama_index.llms.openai import OpenAI  # 导入 OpenAI LLM 类
from llama_index.embeddings.openai import OpenAIEmbedding  # 导入 OpenAI Embedding 类

# --- 配置ECOVAI API ---
ECOVAI_API_KEY = "sk-w20CB5cuk4bZgTLmgMyWczkGSsL1iwLDieapWVfwYsRufklO"
ECOVAI_BASE_URL = "https://api.ecovai.cn/v1"
ECOVAI_LLM_MODEL = "DeepSeek-V3-0324"
ECOVAI_EMBEDDING_MODEL = "text-embedding-3-small"

# 配置全局的 LLM (用于问答生成)
# 使用gpt-4作为模型名称以绕过LlamaIndex的模型验证，实际会调用DeepSeek-V3-0324
Settings.llm = OpenAI(
    model="gpt-4",  # 使用LlamaIndex认识的模型名称
    api_key=ECOVAI_API_KEY,
    api_base=ECOVAI_BASE_URL,
    # 通过额外参数传递实际的模型名称
    additional_kwargs={"model": ECOVAI_LLM_MODEL}
)

# 配置全局的 Embedding Model (用于文本向量化)
Settings.embed_model = OpenAIEmbedding(
    model=ECOVAI_EMBEDDING_MODEL,
    api_key=ECOVAI_API_KEY,
    api_base=ECOVAI_BASE_URL,
)

# 第一行代码：导入相关的库 (已在上方完成)
# 第二行代码：加载数据
documents = SimpleDirectoryReader(input_files=["90-文档-Data/黑悟空/设定.txt"]).load_data()
# 第三行代码：构建索引
index = VectorStoreIndex.from_documents(documents)
# 第四行代码：创建问答引擎
query_engine = index.as_query_engine()
# 第五行代码: 开始问答
print(query_engine.query("黑神话悟空中有哪些战斗工具?"))
