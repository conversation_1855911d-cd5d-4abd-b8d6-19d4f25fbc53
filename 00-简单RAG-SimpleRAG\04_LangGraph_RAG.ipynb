{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1fefeded-cf10-4a60-b969-cb7c120b0052", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["# 1. 加载文档\n", "from langchain_community.document_loaders import WebBaseLoader\n", "loader = WebBaseLoader(\n", "    web_paths=(\"https://zh.wikipedia.org/wiki/黑神话：悟空\",)\n", ")\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": 2, "id": "046fb560-4ec0-4b25-92a2-a091b103d0ae", "metadata": {}, "outputs": [], "source": ["# 2. 文档分块\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "all_splits = text_splitter.split_documents(docs)"]}, {"cell_type": "code", "execution_count": 3, "id": "e4e82774-268d-4549-906a-8447d310846d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\Venv\\langchain_250321\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["# 3. 设置嵌入模型\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "embeddings = HuggingFaceEmbeddings(\n", "    model_name=\"BAAI/bge-small-zh-v1.5\",\n", "    model_kwargs={'device': 'cpu'},\n", "    encode_kwargs={'normalize_embeddings': True}\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "d5d8cb6e-286f-4e86-94a3-0ce0bc3d812d", "metadata": {}, "outputs": [{"data": {"text/plain": ["['604669d1-40eb-4a62-9584-e973179bffdd',\n", " '5ef199b6-3764-48a9-aaa9-8631c332dd79',\n", " '105c0f5c-48fd-4f08-a4fb-7798dbbad476',\n", " 'ceb97d36-c737-4311-bd0b-73e1551f2023',\n", " '639620ef-eb56-4ff2-a1ba-c6b94da1dafe',\n", " '331148e2-5f88-4938-866a-1b25c71a3331',\n", " 'a56dbfef-983a-4067-9d35-96f936676c9f',\n", " 'e77501a8-f91b-4a5a-a461-be92666fa9b8',\n", " 'b0771461-eddc-4a96-a338-2dcc6cf61e6c',\n", " '05e64899-c094-4de6-9e45-9f607a198aea',\n", " '35c28dbc-04d3-4708-8283-91f00416709c',\n", " 'bf110bbf-8350-44e1-abcc-0949cef94ab2',\n", " '77b227ab-580b-4921-ad21-10650f6fed41',\n", " '5feea8e3-baf4-4f1a-ab58-9067c0747106',\n", " '9fec31c1-b267-4f9d-8f44-8c803e14f325',\n", " 'f45ce91f-9bd5-4c0c-9eed-c90ed79e4e0e',\n", " '306eda04-b798-41a3-aa54-a53be73e53a5',\n", " '49bd7b4e-c51f-4fcb-b370-3297b4dd5b20',\n", " 'd88fc80b-f89c-4277-8b7f-b8a2b5430986',\n", " '16fc58cd-75ed-4529-b7ae-a7bb08825159',\n", " '44a1f23e-8fa1-435d-9031-40038362ee9d',\n", " 'f507dc8f-bf14-4fc7-abc3-71531eec82cc',\n", " '7203a50e-6efd-41cb-a6b0-ed23fd11cf37',\n", " '8a43eb83-6501-4c8d-9c77-95e72858206a',\n", " '33ceeb43-e1dd-451f-987f-e3bd85052f47',\n", " '18dc2566-8684-4955-b009-e6f977cf381b',\n", " '4d66af7c-d128-4d51-8dc7-1c48707a6490',\n", " '3bb3acdd-e4d7-445e-b14c-f38f48aa1670',\n", " '62e35c0d-3420-4d1c-bc32-0a876cb118ce',\n", " 'b09a743f-4242-498b-8ae8-a896d7814fb1',\n", " '15825ef8-28b4-4ada-bc0d-f479fe0a1140',\n", " '763b2243-adc2-44f7-b1f4-812d04785e22',\n", " '5fde3111-4154-4a31-880a-f666ead15ecd',\n", " '4ec34196-73e0-4cc9-b11c-e6617833615f',\n", " '818d1d66-155d-4ceb-a879-80f131be1a7d',\n", " 'e28713fa-d311-4db2-8577-412faf3d5fdd']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. 创建向量存储\n", "from langchain_core.vectorstores import InMemoryVectorStore\n", "vector_store = InMemoryVectorStore(embeddings)\n", "vector_store.add_documents(all_splits)"]}, {"cell_type": "code", "execution_count": 5, "id": "5174db19-f845-4e7e-a86c-00c1eadf1bd9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\Venv\\langchain_250321\\Lib\\site-packages\\langsmith\\client.py:277: LangSmithMissingAPIKeyWarning: API key must be provided when using hosted LangSmith API\n", "  warnings.warn(\n"]}], "source": ["# 5. 定义RAG提示词\n", "from langchain import hub\n", "prompt = hub.pull(\"rlm/rag-prompt\")"]}, {"cell_type": "code", "execution_count": 6, "id": "97f96b94-a43a-4737-aa90-c0c24ed337c7", "metadata": {}, "outputs": [], "source": ["# 6. 定义应用状态\n", "from typing import List\n", "from typing_extensions import TypedDict\n", "from langchain_core.documents import Document\n", "class State(TypedDict):\n", "    question: str\n", "    context: List[Document]\n", "    answer: str"]}, {"cell_type": "code", "execution_count": 7, "id": "d07b5a8e-7592-4135-a1ca-51f3ca71c91e", "metadata": {}, "outputs": [], "source": ["# 7. 定义检索步骤\n", "def retrieve(state: State):\n", "    retrieved_docs = vector_store.similarity_search(state[\"question\"])\n", "    return {\"context\": retrieved_docs}"]}, {"cell_type": "code", "execution_count": 8, "id": "22a62256-00e4-464e-8cee-46559757bcd9", "metadata": {}, "outputs": [], "source": ["# 8. 定义生成步骤\n", "def generate(state: State):\n", "    from langchain_openai import ChatOpenAI\n", "    llm = ChatOpenAI(model=\"gpt-4\")\n", "    docs_content = \"\\n\\n\".join(doc.page_content for doc in state[\"context\"])\n", "    messages = prompt.invoke({\"question\": state[\"question\"], \"context\": docs_content})\n", "    response = llm.invoke(messages)\n", "    return {\"answer\": response.content}"]}, {"cell_type": "code", "execution_count": 9, "id": "d4e808fc-5110-4488-bce0-03af79564b49", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 9. 构建和编译应用\n", "from langgraph.graph import START, StateGraph\n", "graph = (\n", "    StateGraph(State)\n", "    .add_sequence([retrieve, generate])\n", "    .add_edge(START, \"retrieve\")\n", "    .compile()\n", ")\n", "\n", "# 显示图结构\n", "from IPython.display import Image, display\n", "from langchain_core.runnables.graph import CurveStyle, MermaidDrawMethod, NodeStyles\n", "\n", "display(\n", "    Image(\n", "        graph.get_graph().draw_mermaid_png(\n", "            draw_method=MermaidDrawMethod.API,\n", "        )\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "ba5cf622-3fdc-4090-a2f8-355ac5b728e5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "问题: 黑悟空有哪些游戏场景？\n", "答案: 《黑神话：悟空》的游戏场景中融合了很多中国的文化和自然地标，比如重慶的大足石刻、山西省的小西天、南禅寺、铁佛寺、广胜寺和鹳雀楼等。游戏的故事发生在《西游记》之后，玩家在游戏中需要探索并通过一系列的冒险任务，寻找遗失根器并尝试复活孙悟空。游戏的篇章设定为六个章节，分别为“火照黑云”、“风起黄昏”、“夜生白露”、“曲度紫鸳”、“日落红尘”和“未竟”。\n"]}], "source": ["# 10. 运行查询\n", "question = \"黑悟空有哪些游戏场景？\"\n", "response = graph.invoke({\"question\": question})\n", "print(f\"\\n问题: {question}\")\n", "print(f\"答案: {response['answer']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "7b701ed9-2058-4909-94f9-a308ed524df5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "langchain_250321", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}